"use client";

import { useState, useEffect } from "react";
import { MicIcon } from "lucide-react";
import { Button } from "./ui/button";
import { useLanguageStore } from "@/stores/language-store";

type MicrophoneProps = {
    setOutput: (value: string) => void;
    setErrorMessage: (value: string | null) => void;
};

export default function Microphone({
    setOutput,
    setErrorMessage,
}: MicrophoneProps) {
    const { language: sourceLanguage } = useLanguageStore();
    const [recording, setRecording] = useState(false);
    const [mediaRecorder, setMediaRecorder] = useState<MediaRecorder | null>(
        null
    );
    const [stream, setStream] = useState<MediaStream | null>(null);

    useEffect(() => {
        // Cleanup function to stop the media recorder and release the stream
        return () => {
            if (mediaRecorder && mediaRecorder.state !== "inactive") {
                mediaRecorder.stop();
            }
            if (stream) {
                stream.getTracks().forEach((track) => track.stop());
            }
        };
    }, [mediaRecorder, stream]);

    let chunks: Blob[] = [];

    function createMediaRecorder() {
        if (typeof window !== "undefined") {
            navigator.mediaDevices
                .getUserMedia({ audio: true })
                .then((newStream) => {
                    setStream(newStream);
                    const newMediaRecorder = new MediaRecorder(newStream);
                    newMediaRecorder.onstart = () => {
                        chunks = [];
                    };
                    newMediaRecorder.ondataavailable = (e) => {
                        chunks.push(e.data);
                    };
                    newMediaRecorder.onstop = async () => {
                        const audioBlob = new Blob(chunks, {
                            type: "audio/webm",
                        });

                        try {
                            await onMediaRecorderStop(audioBlob);
                        } catch (error) {
                            console.error("Error processing audio:", error);
                            setErrorMessage(
                                "Failed to process audio recording"
                            );
                        }
                    };
                    setMediaRecorder(newMediaRecorder);
                })
                .catch((err) => {
                    console.error("Error accessing microphone:", err);
                    setErrorMessage("Error accessing microphone");
                });
        }
    }

    useEffect(() => {
        if (mediaRecorder) {
            mediaRecorder.start();
            setRecording(true);
        }
    }, [mediaRecorder]);

    const startRecording = () => {
        setErrorMessage(null);
        createMediaRecorder();
    };

    const stopRecording = () => {
        if (mediaRecorder) {
            mediaRecorder.stop();
            setMediaRecorder(null);
            setRecording(false);
            // Release the stream when stopping the recording
            if (stream) {
                stream.getTracks().forEach((track) => track.stop());
                setStream(null);
            }
        }
    };

    async function onMediaRecorderStop(audioBlob: Blob) {
        try {
            // Create FormData to send the audio file
            const formData = new FormData();
            formData.append("audio", audioBlob, "audio.webm");
            formData.append("language", sourceLanguage.toLowerCase());

            const response = await fetch("https://chat-translator-dashoard.vercel.app/api/transcribe", {
                method: "POST",
                body: formData,
                headers: {
                    // Explicitly set the origin to help with CORS debugging
                    'Origin': window.location.origin
                }
            });

            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.error || "Transcription failed");
            }

            if (data.success && data.transcription) {
                setOutput(data.transcription);
                setErrorMessage(null);
            } else {
                throw new Error("No transcription received");
            }
        } catch (error) {
            console.error("Transcription error:", error);
            setErrorMessage(
                error instanceof Error
                    ? error.message
                    : "Failed to transcribe audio"
            );
        }
    }

    return (
        <Button
            className="rounded-r-md w-10 h-10 flex-row bg-green-500 hover:bg-green-600 text-white font-semibold relative border-none"
            variant={"outline"}
            type="button"
            onClick={recording ? stopRecording : startRecording}
        >
            {recording && <div className="absolute h-2 w-2 rounded-full bg-red-500 animate-pulse top-1 right-1"></div>}
            <MicIcon size={"18"} />
        </Button>
    );
}
