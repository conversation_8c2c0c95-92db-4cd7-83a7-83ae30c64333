import { useMutation } from "@tanstack/react-query";

// Types for Transcription API
export interface TranscriptionRequest {
  audio: Blob;
  language: string;
}

export interface TranscriptionResponse {
  success: boolean;
  transcription: string;
  error?: string;
}

// Base transcription API URL
const TRANSCRIPTION_API_URL = import.meta.env.VITE_TRANSCRIPTION_API_URL || 'http://localhost:8200';

/**
 * React Query mutation for audio transcription
 */
export function useTranscriptionMutation() {
  return useMutation({
    mutationFn: async ({ audio, language }: TranscriptionRequest): Promise<TranscriptionResponse> => {
      try {
        // Create FormData to send the audio file
        const formData = new FormData();
        formData.append("audio", audio, "audio.webm");
        formData.append("language", language.toLowerCase());

        const response = await fetch(`${TRANSCRIPTION_API_URL}/transcribe/simple`, {
          method: "POST",
          body: formData,
          headers: {
            // Explicitly set the origin to help with CORS debugging
            'Origin': window.location.origin
          }
        });

        const data = await response.json();

        if (!response.ok) {
          throw new Error(data.error || "Transcription failed");
        }

        if (data.success && data.transcription) {
          return {
            success: true,
            transcription: data.transcription
          };
        } else {
          throw new Error("No transcription received");
        }
      } catch (error) {
        console.error("Transcription error:", error);
        throw new Error(
          error instanceof Error
            ? error.message
            : "Failed to transcribe audio"
        );
      }
    },
  });
}

/**
 * Hook for transcription with custom error handling
 */
export function useTranscriptionWithErrorHandling() {
  return useMutation({
    mutationFn: async ({ audio, language }: TranscriptionRequest): Promise<string> => {
      const formData = new FormData();
      formData.append("audio", audio, "audio.webm");
      formData.append("language", language.toLowerCase());

      const response = await fetch(`${TRANSCRIPTION_API_URL}/transcribe/simple`, {
        method: "POST",
        body: formData,
        headers: {
          'Origin': window.location.origin
        }
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Transcription failed");
      }

      if (data.success && data.transcription) {
        return data.transcription;
      } else {
        throw new Error("No transcription received");
      }
    },
  });
}
